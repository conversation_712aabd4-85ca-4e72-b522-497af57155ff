"use strict";
"use sound";
import Page = require("yunos/page/Page");
import StackRouter = require("yunos/appmodel/StackRouter");

class App extends Page {

    theme = "default";

    onStart() {
        let router = new StackRouter();

        router.container = this.window;

        // route will be register automatically
        // if you follow the automatic module loading mechanism
        // route path -> "home"
        // presenter  -> "src/presenter/HomePresenter.js"
        // view       -> "res/{qualifier}/layout/home.xml"
        // model      -> "src/model/HomeModel.js"
        router.navigate("home");
    }

}

export = App;
