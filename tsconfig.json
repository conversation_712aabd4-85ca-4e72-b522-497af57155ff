{"compilerOptions": {"module": "commonjs", "moduleResolution": "node", "lib": ["es6"], "typeRoots": ["node_modules/@ali/caf-types/@types", "node_modules/@types"], "types": ["node", "yunos"], "target": "es2016", "noImplicitAny": false, "noEmitOnError": false, "alwaysStrict": false, "preserveConstEnums": true, "sourceMap": false, "outDir": "./src", "baseUrl": ".", "skipLibCheck": true, "paths": {"*": ["*", "node_modules/@ali/caf-types/*", "node_modules/@banma/hdt-ui-types/*", "node_modules/@banma/hdt-types/*"]}}, "include": ["ts/**/*.ts"], "exclude": ["**/*.spec.ts", "node_modules", "src/**/*.ts"]}